import 'package:app_links/app_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/universal_link/comment_link.dart';
import 'package:swadesic/util/universal_link/external_review_link.dart';
import 'package:swadesic/util/universal_link/post_link.dart';
import 'package:swadesic/util/universal_link/product_link.dart';
import 'package:swadesic/util/universal_link/store_and_user_link.dart';

class HandleUrl {
  //region Constructor
  HandleUrl() {
    //If web
    if (kIsWeb) {
      //If web
      if (AppConstants.webChangedUrl.isNotEmpty &&
          Uri.parse(AppConstants.webChangedUrl).pathSegments.isNotEmpty) {
        debugPrint(
            "In HandleUrl we get the incoming link: ${AppConstants.webChangedUrl}");
        handleAndNavigate(url: Uri.parse(AppConstants.webChangedUrl));
      }
    } else {
      //Get app data
      urlListener();
    }
  }
  //endregion

  //region Url listener
  /// Initializes the `AppLinks` instance and listens for app link events.
  ///
  /// This method is responsible for setting up the necessary infrastructure to handle app link navigation.
  /// It creates an instance of `AppLinks` and listens for any incoming app link events using the `allUriLinkStream`.
  /// When an app link event is received, the method calls the `handleAndNavigate()` function to process the link and navigate the user accordingly.
  void urlListener() async {
    //App link initialize
    final appLinks = AppLinks();

    ///If app is not opened then fetch the latest app link
    if (!AppConstants.isAppOpen) {
      //If Latest app link
      Uri? url = await appLinks.getLatestAppLink();
      //If url is not null then process the url
      if (url != null && url.pathSegments.isNotEmpty) {
        debugPrint("In HandleUrl we get the incoming link is ${url}");
        await handleAndNavigate(url: url);
      }
    }
    //Listen app link
    appLinks.uriLinkStream.listen((link) async {
      ///If app already open then fetch the link from the listener
      if (AppConstants.isAppOpen) {
        Uri? uri = link;
        debugPrint("In HandleUrl we get the incoming link is ${uri}");
        await handleAndNavigate(url: uri);
      }
    });
    //Mark that app is opened
    AppConstants.isAppOpen = true;
  }
  //endregion

  ///Handle web app url
  //region Handle app url
  Future<void> handleAndNavigate({required Uri url}) async {
    //Web url
    // RegExp webUrl = RegExp(r'^(https?:\/\/)?(www\.)?swadesic\.com\/web');
    // RegExp productPattern = RegExp(r'^p\?r=P\d+');
    // RegExp productCommentPattern = RegExp(r'^co/\?s=S[A-Za-z0-9]+&p=P[A-Za-z0-9]+&cId=\d+');
    // RegExp postPattern = RegExp(r'^(po|co)\?r=(PO|CO)\d+');
    // RegExp urlValidate = RegExp(r'^(https?:\/\/)?' // Protocol (optional)
    // r'([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,}' // Domain
    // r'(:\d+)?' // Port (optional)
    // r'(\/[^\s?]*)?' // Path (optional)
    // r'(\?([a-zA-Z0-9_\-]+=[a-zA-Z0-9_\-]+)(&[a-zA-Z0-9_\-]+=[a-zA-Z0-9_\-]+)*)?$)');
    // String urlData = '';
    Uri completeDecodedUrl = Uri();

    //Decode url path segment if the path segment is encoded and not empty
    if (url.pathSegments.isNotEmpty &&
        CommonMethods.canDecodeBase32(url.pathSegments.first)) {
      completeDecodedUrl = Uri.parse(
          "https://xyz.com/${CommonMethods.decodeBase32(url.pathSegments.first)}");
    } else {
      completeDecodedUrl = url;
    }

    try {
      await Future.delayed(const Duration(seconds: 1));

      ///0. Check is it a web url
      if (url.toString().contains("/web")) {
        //print(url.toString());
        CommonMethods.opeAppWebView(
            webUrl: url.toString(),
            context: AppConstants.userStoreCommonBottomNavigationContext!);
        return;
      }

      ///0.1. Check if it's an FAQ URL (check original URL first, not decoded)
      if (url.pathSegments.isNotEmpty && url.pathSegments.first == 'faq') {
        FaqNavigation.handleFaqLink(
          AppConstants.userStoreCommonBottomNavigationContext!,
          url, // Use original URL, not decoded
        );
        return;
      }

      //Check if the url has 'ic' parameter
      if (completeDecodedUrl.queryParameters.containsKey('ic') &&
          completeDecodedUrl.queryParameters['ic'] != null) {
        //Save referral code
        AppDataService().saveReferralCode(
            referralCode: completeDecodedUrl.queryParameters['ic']!);
        //print(AppConstants.appData.referralCode);
      }

      //Product slug
      if (completeDecodedUrl.pathSegments.length == 3 &&
          completeDecodedUrl.pathSegments[1] == 'product' &&
          completeDecodedUrl.pathSegments[2].isNotEmpty) {
          ProductSlugLink(
              completeDecodedUrl.pathSegments[0],
              completeDecodedUrl.pathSegments[2]
          );
      }


      ///1. Post
      if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('PO')) {
        PostLink(completeDecodedUrl.queryParameters['r']!);
      }

      ///2. Product
      else if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('P')) {
        ProductLink(completeDecodedUrl.queryParameters['r']!);
      }

      ///3. External Review (Firebase deep link)
      else if (completeDecodedUrl.queryParameters.containsKey('pr') &&
          completeDecodedUrl.queryParameters.containsKey('t') &&
          (completeDecodedUrl.queryParameters.containsKey('ur'))) {
        // Get the token from the URL
        String token = completeDecodedUrl.queryParameters['t']!;
        String productReference = completeDecodedUrl.queryParameters['pr']!;
        String userReference = completeDecodedUrl.queryParameters['ur']!;

        // Handle external review link with the token
        ExternalReviewLink(token,
            productReference: productReference, userReference: userReference);
      }

      ///5. Store or user
      else {
        StoreAndUserLink(
            handleAndUserName: completeDecodedUrl.pathSegments.first,
            isSuperLink: (completeDecodedUrl.path.contains('super_link') ||
                completeDecodedUrl.path.contains('super_link/')));
      }
    } catch (error) {
      //print(error);
    }
  }
//endregion
}

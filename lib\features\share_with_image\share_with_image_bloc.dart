import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:rxdart/rxdart.dart';
import 'package:share_plus/share_plus.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class ShareWithImageBloc {
  // region Common Variables
  BuildContext context;
  bool openForOrder = false;
  String productLink = "";
  String shareMessageUrl = "";
  final String message;
  String? storeName;
  String? productName;
  String? productBrand;
  String? storeIconUrl;
  String? postText;
  String? postCreatorName;
  String? postCreatorIcon;
  String? imageLink;
  String?
      attachmentImagePath; // Path to local image file to be shared as attachment
  EntityType entityType = EntityType.PRODUCT;
  WebSocketChannel? _channel;
  String? selectedChatId;
  String? selectedChatName;
  String? selectedChatIcon;
  Map<String, dynamic>? selectedChat; // Store the entire selected chat object
  bool isLoading = false;
  bool isSending = false;
  String? creatorId; // ID of the creator of the shared object

  // endregion

  //region Controller
  final commentCtrl = StreamController.broadcast();
  final storeDetailsCtrl = BehaviorSubject<Map<String, dynamic>>();
  final postDetailsCtrl = BehaviorSubject<Map<String, dynamic>>();
  final chatsCtrl = BehaviorSubject<List<Map<String, dynamic>>>();
  final selectedChatCtrl = BehaviorSubject<Map<String, dynamic>>();
  final loadingCtrl = BehaviorSubject<bool>.seeded(false);
  final sendingCtrl = BehaviorSubject<bool>.seeded(false);
  //endregion

  //region Text Editing Controller
  TextEditingController messageFieldCtrl = TextEditingController();
  TextEditingController searchFieldCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ShareWithImageBloc(
    this.context,
    this.productLink,
    this.message, {
    this.storeName,
    this.productName,
    this.productBrand,
    this.storeIconUrl,
    this.postText,
    this.postCreatorName,
    this.postCreatorIcon,
    this.imageLink,
    this.attachmentImagePath,
    required this.entityType,
  });

  // endregion

  // region Init
  void init() {
    debugPrint('DEBUG: ShareWithImageBloc init');
    // Convert encoded mentions to display text for sharing
    String displayText = MentionParser.convertToDisplayText(message);
    messageFieldCtrl.text = displayText;
    _emitStoreDetails();
    _emitPostDetails();
    fetchChats();
  }

  // endregion

  //region Emit Store Details
  void _emitStoreDetails() {
    debugPrint('DEBUG: Emitting store details:');
    debugPrint('DEBUG: Store Name: $storeName');
    debugPrint('DEBUG: Product Name: $productName');
    debugPrint('DEBUG: Brand Name: $productBrand');
    debugPrint('DEBUG: Store Icon: $storeIconUrl');

    if (!storeDetailsCtrl.isClosed) {
      Map<String, dynamic> details = {
        'storeName': storeName,
        'productName': productName,
        'productBrand': productBrand,
        'storeIconUrl': storeIconUrl,
      };
      storeDetailsCtrl.add(details);
      debugPrint('DEBUG: Store details emitted to stream');
    } else {
      debugPrint('DEBUG: Store details controller is closed!');
    }
  }
  //endregion

  //region Share Link
  void shareLinkMessage() async {
    String messageText = messageFieldCtrl.text;
    shareMessageUrl = "$messageText \n $productLink";

    // If we have an attachment image, share both text and image
    if (attachmentImagePath != null && attachmentImagePath!.isNotEmpty) {
      await Share.shareXFiles(
        [XFile(attachmentImagePath!)],
        text: shareMessageUrl,
      );
    } else {
      // Share only text
      await Share.share(shareMessageUrl);
    }
  }

  //endregion

  //region Copy Url With Message
  void copyMessageUrl() {
    String messageText = messageFieldCtrl.text;
    shareMessageUrl = "$messageText \n $productLink";
    CommonMethods.copyText(context, shareMessageUrl);

    // FlutterClipboard.copy(shareMessageUrl).then((result) {
    //   const snackBar = SnackBar(content: Text("Copied to Clipboard"));
    //   ScaffoldMessenger.of(context).showSnackBar(snackBar);
    // }
    // );
  }

  //endregion
  //region Open facebook
  void openFacebook() async {
    String message = messageFieldCtrl.text;
    //print("https://www.facebook.com/sharer/sharer.php?u=$productLink&t=$message");
    await launch("fb://feed");
  }

  //endregion

  //region Open Whats App
  void openWhatsapp() async {
    String messageText = messageFieldCtrl.text;
    shareMessageUrl = messageText + productLink;

    // If we have an attachment image, share both text and image via WhatsApp
    if (attachmentImagePath != null && attachmentImagePath!.isNotEmpty) {
      await Share.shareXFiles(
        [XFile(attachmentImagePath!)],
        text: shareMessageUrl,
      );
    } else {
      // Share only text via WhatsApp web
      String url = "https://wa.me/?text=$shareMessageUrl";
      await launchUrl(Uri.parse(url));
    }
  }
  //endregion

  //region Emit Post Details
  void _emitPostDetails() {
    debugPrint('DEBUG: Emitting post details:');
    debugPrint('DEBUG: Post Text: $postText');
    debugPrint('DEBUG: Post Creator Name: $postCreatorName');
    debugPrint('DEBUG: Post Creator Icon: $postCreatorIcon');

    if (!postDetailsCtrl.isClosed) {
      Map<String, dynamic> details = {
        'postText': postText,
        'postCreatorName': postCreatorName,
        'postCreatorIcon': postCreatorIcon,
      };
      postDetailsCtrl.add(details);
      debugPrint('DEBUG: Post details emitted to stream');
    } else {
      debugPrint('DEBUG: Post details controller is closed!');
    }
  }
  //endregion

  //region Fetch Chats
  Future<void> fetchChats() async {
    try {
      loadingCtrl.add(true);
      isLoading = true;

      // Get the creator ID based on the entity type
      if (postCreatorName != null) {
        // TODO: Get the creator ID from the backend if needed
      }

      // Fetch active chats using the get_chats/v2 API
      final response = await http.get(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/chats/get_chats/v2?chatType=ACTIVE&limit=20&offset=0'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final List<Map<String, dynamic>> chats =
            data.map((chat) => chat as Map<String, dynamic>).toList();

        // Sort chats to show the creator first if they exist in the chat list
        if (creatorId != null) {
          chats.sort((a, b) {
            if (a['connecting_id'] == creatorId) return -1;
            if (b['connecting_id'] == creatorId) return 1;
            return 0;
          });
        }

        chatsCtrl.add(chats);
      } else {
        // Handle error
        debugPrint('Failed to load chats: ${response.statusCode}');
        chatsCtrl.add([]);
      }
    } catch (e) {
      debugPrint('Error fetching chats: $e');
      chatsCtrl.add([]);
    } finally {
      loadingCtrl.add(false);
      isLoading = false;
    }
  }
  //endregion

  //region Search Chats
  Future<void> searchChats(String query) async {
    if (query.isEmpty) {
      fetchChats();
      return;
    }

    try {
      loadingCtrl.add(true);
      isLoading = true;

      final response = await http.get(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/chats/search_chats/searchQuery=$query/limit=10/offset=0'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final List<Map<String, dynamic>> chats =
            data.map((chat) => chat as Map<String, dynamic>).toList();

        // Sort chats to show the creator first if they exist in the chat list
        if (creatorId != null) {
          chats.sort((a, b) {
            if (a['connecting_id'] == creatorId) return -1;
            if (b['connecting_id'] == creatorId) return 1;
            return 0;
          });
        }

        chatsCtrl.add(chats);
      } else {
        // Handle error
        debugPrint('Failed to search chats: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching chats: $e');
    } finally {
      loadingCtrl.add(false);
      isLoading = false;
    }
  }
  //endregion

  //region Select Chat
  void selectChat(Map<String, dynamic> chat) {
    // Store both chat_id and connecting_id for the selected chat
    final String? chatId = chat['chat_id'];
    final String? connectingId = chat['connecting_id'];

    // Create a unique identifier that includes both IDs
    final String uniqueIdentifier = '$chatId:$connectingId';

    // Check if this is the currently selected chat/contact
    bool isCurrentlySelected = false;

    // If we have a selected chat, check if it's the same as the one being tapped
    if (selectedChat != null) {
      final String? selectedChatId = selectedChat!['chat_id'];
      final String? selectedConnectingId = selectedChat!['connecting_id'];

      // Compare both chat_id and connecting_id to determine if it's the same chat
      if (chatId == selectedChatId && connectingId == selectedConnectingId) {
        isCurrentlySelected = true;
      }
    }

    // If the chat is already selected, unselect it
    if (isCurrentlySelected) {
      selectedChatId = null;
      selectedChatName = null;
      selectedChatIcon = null;
      selectedChat = null;
      selectedChatCtrl.add({});
    } else {
      // Otherwise, select the chat
      selectedChatId = chatId ?? connectingId;
      selectedChatName = chat['chat_name'];
      selectedChatIcon = chat['chat_icon'];
      selectedChat = chat; // Store the entire chat object
      selectedChatCtrl.add(chat);
    }
  }
  //endregion

  //region Send Message to Chat
  Future<void> sendMessageToChat(String sharedObjectReference) async {
    if (selectedChatId == null) {
      return;
    }

    try {
      sendingCtrl.add(true);
      isSending = true;

      // Check if chat exists or needs to be created
      String chatId = selectedChatId!;

      if (!chatId.contains('-')) {
        // This is a connecting_id, not a chat_id, so we need to create a chat
        final createResponse = await http.post(
          Uri.parse(
              '${AppConstants.newMessaging_baseUrl}/api/chats/create_chat'),
          headers: {
            'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
            'Content-Type': 'application/json',
          },
          body: json.encode({
            'chat_type': 'DIRECT',
            'member_ids': [selectedChatId],
          }),
        );

        if (createResponse.statusCode == 201 ||
            createResponse.statusCode == 200) {
          final data = json.decode(createResponse.body);
          chatId = data['chat']['chat_id'];
        } else {
          throw Exception(
              'Failed to create chat: ${createResponse.statusCode}');
        }
      }

      // Prepare attachments list
      List<Map<String, dynamic>> attachments = [];

      // If we have an attachment image, upload it first and get the URL
      if (attachmentImagePath != null && attachmentImagePath!.isNotEmpty) {
        try {
          final uploadedFileData = await _uploadImageFile(attachmentImagePath!);
          attachments.add(uploadedFileData);
        } catch (e) {
          debugPrint('Error uploading attachment image: $e');
          // Continue without attachment if upload fails
        }
      }

      // Send the message with the simple object reference and attachments
      final messageResponse = await http.post(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/messages/send_message'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'chat_id': chatId,
          'content': messageFieldCtrl.text,
          'message_type': attachments.isNotEmpty ? 'MEDIA' : 'TEXT',
          'object': sharedObjectReference,
          'attachments': attachments,
        }),
      );

      if (messageResponse.statusCode == 201) {
        // Message sent successfully
        messageFieldCtrl.clear();
        CommonMethods.toastMessage('Message sent successfully', context);
        // Close the share screen safely
        if (context.mounted) {
          Navigator.pop(context);
        }
      } else {
        throw Exception(
            'Failed to send message: ${messageResponse.statusCode}');
      }
    } catch (e) {
      debugPrint('Error sending message: $e');
      if (context.mounted) {
        CommonMethods.toastMessage('Failed to send message: $e', context);
      }
    } finally {
      sendingCtrl.add(false);
      isSending = false;
    }
  }
  //endregion

  //region Upload Image File
  Future<Map<String, dynamic>> _uploadImageFile(String filePath) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(AppConstants.newMessaging_uploadFiles),
      );

      // Add authorization header
      request.headers['Authorization'] =
          'Bearer ${AppConstants.appData.newMessagingToken}';

      // Add file to the request
      request.files.add(await http.MultipartFile.fromPath('files', filePath));

      // Send the request
      final response = await request.send();
      final responseData = await http.Response.fromStream(response);
      final jsonResponse = json.decode(responseData.body);

      if (jsonResponse['success']) {
        // Get the file data from the response
        final fileData = jsonResponse['data'][0];
        return {
          'url': fileData['url'] ?? '',
          'originalName': 'support_score_card.png',
          'size': File(filePath).lengthSync(),
          'mimetype': fileData['mimetype'] ?? 'image/png',
          'filename': fileData['file_name'] ?? '',
          'type': fileData['file_type'] ?? 'image',
        };
      }

      throw Exception('Failed to upload file');
    } catch (e) {
      debugPrint('Error uploading image file: $e');
      rethrow;
    }
  }
  //endregion

  //region Dispose
  void dispose() {
    commentCtrl.close();
    storeDetailsCtrl.close();
    postDetailsCtrl.close();
    chatsCtrl.close();
    selectedChatCtrl.close();
    loadingCtrl.close();
    sendingCtrl.close();
    messageFieldCtrl.dispose();
    searchFieldCtrl.dispose();
    _channel?.sink.close();
  }
  //endregion
}

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/services/product_slug_service/product_slug_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';

class ProductLink{
  String productReference  = "";

  ProductLink(this.productReference) {
    action();
  }


  //region Action
  void action(){
    var screen = BuyerViewSingleProductScreen(productReference:productReference,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);

  }
//endregion

}

class ProductSlugLink{
  String storeHandle = "";
  String productSlug = "";

  ProductSlugLink(this.storeHandle,this.productSlug) {
    action();
  }

  //region Action
  Future<void> action() async {
    String storeReference = await StoreAndUserReferenceServices().getStoreAndUserReferences(handleAndUserName: storeHandle);

    String? productReference = await ProductSlugService().getProductReferenceFromSlug(
      storeReference: storeReference,
      productSlug: productSlug,
    );
    
    if (productReference != null) {
      var screen = BuyerViewSingleProductScreen(productReference: productReference);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    } else {
      // Handle the case when product reference is not found
      if (AppConstants.currentSelectedTabContext.mounted) {
        ScaffoldMessenger.of(AppConstants.currentSelectedTabContext).showSnackBar(
          const SnackBar(content: Text('Product not found')),
        );
      }
    }

  }
//endregion

}